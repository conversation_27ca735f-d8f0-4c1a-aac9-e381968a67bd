"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { addQuickYouTubeBlog, extractYouTubeVideoId } from '@/apis/youtubeBlogs';
import { toast } from 'sonner';
import { Plus, Youtube } from 'lucide-react';

const AddYouTubeVideoHelper = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    youtubeUrl: '',
    title: '',
    description: '',
    category: 'General',
    tags: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const categories = [
    'General',
    'Craftsmanship',
    'Process',
    'Education',
    'Techniques',
    'Care',
    'Design',
    'History',
    'Sustainability'
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.youtubeUrl || !formData.title || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Validate YouTube URL
    const videoId = extractYouTubeVideoId(formData.youtubeUrl);
    if (!videoId) {
      toast.error('Please enter a valid YouTube URL');
      return;
    }

    setIsLoading(true);

    try {
      const tags = formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const result = await addQuickYouTubeBlog(
        formData.youtubeUrl,
        formData.title,
        formData.description,
        formData.category,
        tags
      );

      if (result) {
        toast.success('YouTube video added successfully!');
        setFormData({
          youtubeUrl: '',
          title: '',
          description: '',
          category: 'General',
          tags: ''
        });
        setIsOpen(false);
        
        // Refresh the page to show the new video
        window.location.reload();
      } else {
        toast.error('Failed to add video');
      }
    } catch (error) {
      console.error('Error adding video:', error);
      toast.error('Failed to add video');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 z-50 shadow-lg"
        size="lg"
      >
        <Plus className="w-5 h-5 mr-2" />
        Add YouTube Video
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Youtube className="w-5 h-5 text-red-500" />
            Add YouTube Video to Blogs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* YouTube URL */}
            <div>
              <label className="block text-sm font-medium mb-2">
                YouTube URL *
              </label>
              <Input
                type="url"
                placeholder="https://www.youtube.com/watch?v=..."
                value={formData.youtubeUrl}
                onChange={(e) => handleInputChange('youtubeUrl', e.target.value)}
                required
              />
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Video Title *
              </label>
              <Input
                type="text"
                placeholder="Enter video title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Description *
              </label>
              <textarea
                className="w-full p-3 border border-gray-300 rounded-md resize-none"
                rows={4}
                placeholder="Enter video description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                required
              />
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Category
              </label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleInputChange('category', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Tags (comma-separated)
              </label>
              <Input
                type="text"
                placeholder="woodworking, traditional, carving"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
              />
            </div>

            {/* Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isLoading}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? 'Adding...' : 'Add Video'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddYouTubeVideoHelper;
